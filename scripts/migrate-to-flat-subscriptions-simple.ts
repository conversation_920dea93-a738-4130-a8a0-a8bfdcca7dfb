#!/usr/bin/env tsx

/**
 * Simple Migration Script for Flat Subscription Architecture
 * 
 * This script migrates existing subscription data to the new flat collection structure.
 * 
 * Usage:
 *   npm run migrate:flat-subscriptions:simple [env-file]
 * 
 * Examples:
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts .env.production
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts --dry-run
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts --validate-only
 */

import { config } from "dotenv"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { AdminMigrationService } from "../lib/domains/user-subscription/admin-migration.service"

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes('--dry-run')
const isValidateOnly = args.includes('--validate-only')
const isBackupOnly = args.includes('--backup-only')
const isRollback = args.includes('--rollback')
const envFile = args.find(arg => !arg.startsWith('--')) || '.env.local'

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  // Load environment variables from specified .env file
  const envPath = resolve(process.cwd(), envFile)
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  // Initialize Firebase Admin SDK if not already initialized
  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log('✅ Firebase Admin SDK initialized successfully')
    } catch (error) {
      console.error("❌ Firebase admin initialization error:", error)
      process.exit(1)
    }
  }

  return getFirestore()
}

async function performBackupOnly() {
  console.log("📦 Backing up existing subscription data...\n")

  const result = await AdminMigrationService.backupExistingData()

  if (result.success) {
    const { backedUpCount, errors } = result.data!
    console.log(`✅ Backup completed: ${backedUpCount} subscriptions backed up`)

    if (errors.length > 0) {
      console.log("⚠️  Backup warnings:")
      errors.forEach((error: string) => console.log(`  - ${error}`))
    }
  } else {
    console.error("❌ Backup failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }
}

async function validateMigration() {
  console.log("🔍 Validating flat subscription structure...\n")

  const result = await AdminMigrationService.validateMigration()

  if (!result.success) {
    console.error("❌ Validation failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }

  const { isValid, issues, statistics } = result.data!

  console.log("📊 Migration Statistics:")
  console.log(`  Total users: ${statistics.totalUsers}`)
  console.log(`  Users with subscriptions: ${statistics.usersWithSubscriptions}`)
  console.log(`  Total subscription entries: ${statistics.totalEntries}`)
  console.log(`  Free entries: ${statistics.freeEntries}`)
  console.log(`  Stripe entries: ${statistics.stripeEntries}`)
  console.log(`  Users without free entry: ${statistics.usersWithoutFreeEntry}`)

  if (isValid) {
    console.log("\n✅ Migration validation PASSED")
  } else {
    console.log("\n❌ Migration validation FAILED")
    console.log("\n🚨 Issues found:")
    issues.forEach((issue: string, index: number) => {
      console.log(`  ${index + 1}. ${issue}`)
    })
    process.exit(1)
  }
}

async function performRollback() {
  console.log("🚨 EMERGENCY ROLLBACK")
  console.log("This will restore the original subscription structure!")
  console.log("⚠️  All new flat subscription data will be DELETED!")

  // Confirm before proceeding
  console.log("Press Ctrl+C to cancel, or wait 10 seconds to continue...")
  await new Promise((resolve) => setTimeout(resolve, 10000))

  const result = await AdminMigrationService.emergencyRollback()

  if (result.success) {
    const { restoredCount, deletedCount, errors } = result.data!
    console.log(`✅ Rollback completed:`)
    console.log(`  📦 Restored: ${restoredCount} original subscriptions`)
    console.log(`  🗑️  Deleted: ${deletedCount} flat entries`)

    if (errors.length > 0) {
      console.log("⚠️  Rollback warnings:")
      errors.forEach((error: string) => console.log(`  - ${error}`))
    }
  } else {
    console.error("❌ Rollback failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }
}

async function runDryRun() {
  console.log('🔍 [DRY RUN] Full migration preview\n')
  console.log('This is a dry run - no changes will be made')
  
  console.log('Would perform the following actions:')
  console.log('1. 📦 Backup existing subscription data')
  console.log('2. 🔄 Convert subscription documents to flat entries')
  console.log('3. ➕ Ensure all users have free subscription entries')
  console.log('4. 🗑️  Delete original subscription documents')
  console.log('5. 🔍 Validate migration results')
  
  console.log('\nTo perform the actual migration, run without --dry-run flag')
}

async function runFullMigration() {
  console.log("🚀 Starting full flat subscription migration...\n")

  // Confirm before proceeding
  console.log("⚠️  This will completely restructure your subscription data!")
  console.log("⚠️  Make sure you have a database backup!")
  console.log("Press Ctrl+C to cancel, or wait 5 seconds to continue...")

  await new Promise((resolve) => setTimeout(resolve, 5000))

  console.log("\n🔄 Starting migration...")

  const migrationResult = await AdminMigrationService.migrateAllUsers()

  if (!migrationResult.success) {
    console.error(
      "❌ Migration failed:",
      (migrationResult.error as Error)?.message || migrationResult.error
    )
    process.exit(1)
  }

  const { totalProcessed, successfulMigrations, totalEntriesCreated, errors } =
    migrationResult.data!

  console.log(`\n✅ Migration completed:`)
  console.log(`  📊 Total processed: ${totalProcessed}`)
  console.log(`  ✅ Successful: ${successfulMigrations}`)
  console.log(`  📝 Entries created: ${totalEntriesCreated}`)
  console.log(`  ❌ Errors: ${errors.length}`)

  if (errors.length > 0) {
    console.log("\n🚨 Migration errors:")
    errors.forEach((error: string, index: number) => {
      console.log(`  ${index + 1}. ${error}`)
    })
  }

  console.log("\n🔍 Running validation...")
  await validateMigration()

  console.log("\n🎉 Migration completed successfully!")
  console.log("\nNext steps:")
  console.log("1. Test the application with the new subscription system")
  console.log("2. Monitor for any issues")
  console.log("3. Update your application code to use FlatSubscriptionService")
  console.log("4. Remove backup data after confirming everything works")
}

async function main() {
  try {
    // Initialize Firebase Admin SDK
    await initializeFirebase()
    
    console.log('🚀 Flat Subscription Migration Tool (Simple)')
    console.log('============================================')
    
    if (isRollback) {
      await performRollback()
      return
    }
    
    if (isBackupOnly) {
      await performBackupOnly()
      return
    }
    
    if (isValidateOnly) {
      await validateMigration()
      return
    }
    
    if (isDryRun) {
      await runDryRun()
      return
    }
    
    // Full migration
    await runFullMigration()
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Run the script
main().catch((error) => {
  console.error('💥 Script failed:', error)
  process.exit(1)
})
